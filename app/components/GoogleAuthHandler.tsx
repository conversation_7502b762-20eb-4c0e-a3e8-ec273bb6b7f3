"use client";

import { useEffect } from "react";
import { useDispatch } from "react-redux";
import { useSearchParams } from "next/navigation";
import { useCurrentAccount, useDisconnectWallet } from "@mysten/dapp-kit";
import { setUserAuth, setIsExternalWallet } from "@/store/user.store";
import { setIsShowModalLogin } from "@/store/metadata.store";
import Storage from "@/libs/storage";
import { closeSocketInstance, createSocketInstance } from "@/libs/socket";
import { NETWORKS } from "@/utils/contants";

export const GoogleAuthHandler = () => {
  const dispatch = useDispatch();
  const searchParams = useSearchParams();
  const currentAccount = useCurrentAccount();
  const { disconnect } = useDisconnectWallet();

  useEffect(() => {
    const handleGoogleAuth = async () => {
      try {
        // Check if we have a JWT token from the Google OAuth callback via cookie
        const response = await fetch("/api/auth/session-jwt", {
          cache: "no-store",
        });
        if (!response.ok) return;

        const { jwtToken } = await response.json();
        if (!jwtToken) return;

        console.log("Handling Google OAuth success with JWT from cookie");

        // Update Redux/localStorage/axios header via setUserAuth
        dispatch(setUserAuth({ accessToken: jwtToken }));

        // If a wallet is connected, disconnect to avoid overlapping auth states
        try {
          if (currentAccount?.address) {
            disconnect();
          }
        } catch {}

        // Mark login method for analytics/flows (turnkey/google)
        try {
          Storage.setLoginMethod("turnkey");
        } catch {}

        // Reset any external wallet flag
        try {
          dispatch(setIsExternalWallet(false));
        } catch {}

        // Close login modal if open
        try {
          dispatch(setIsShowModalLogin(false));
        } catch {}

        // Handle redirect-after-login
        const redirectAfterLogin = Storage.getRedirectAfterLogin();
        if (redirectAfterLogin) {
          const location = `${window.location.pathname}${window.location.search}`;
          if (location !== redirectAfterLogin) {
            Storage.clearRedirectAfterLogin();
            // Remove JWT from URL before redirecting
            const url = new URL(window.location.href);
            url.searchParams.delete("jwt");
            window.history.replaceState({}, "", url.toString());
            window.location.href = redirectAfterLogin;
            return;
          }
        }

        // Reconnect sockets with new token
        try {
          closeSocketInstance(NETWORKS.SUI);
          createSocketInstance(NETWORKS.SUI, jwtToken);
        } catch {}

        // No URL cleanup needed since we're using cookies

        console.log("Google OAuth authentication completed successfully");
      } catch (error) {
        console.error("Error handling Google OAuth callback:", error);
      }
    };

    handleGoogleAuth();
  }, [searchParams, dispatch, currentAccount?.address, disconnect]);

  return null; // This component doesn't render anything
};
