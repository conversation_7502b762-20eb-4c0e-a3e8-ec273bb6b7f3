"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

export default function AuthErrorPage() {
  const searchParams = useSearchParams();
  const [error, setError] = useState<string>("");

  useEffect(() => {
    const errorParam = searchParams.get("error");
    setError(errorParam || "Unknown authentication error");
  }, [searchParams]);

  const getErrorMessage = (errorCode: string) => {
    switch (errorCode) {
      case "NoNonce":
        return "Authentication failed: No nonce found. Please try logging in again.";
      case "InvalidNonce":
        return "Authentication failed: Invalid nonce. Please try logging in again.";
      case "NoIdToken":
        return "Authentication failed: No ID token received from Google.";
      case "InvalidIdToken":
        return "Authentication failed: Invalid ID token received.";
      case "TokenExchangeFailed":
        return "Authentication failed: Could not exchange authorization code for tokens.";
      case "UserInfoFailed":
        return "Authentication failed: Could not retrieve user information.";
      case "BackendAuthFailed":
        return "Authentication failed: Backend authentication error.";
      case "InvalidState":
        return "Authentication failed: Invalid state parameter.";
      case "MissingCodeVerifier":
        return "Authentication failed: Missing code verifier.";
      case "MissingParameters":
        return "Authentication failed: Missing required parameters.";
      case "CallbackError":
        return "Authentication failed: Error processing callback.";
      case "AuthError":
        return "Authentication failed: General authentication error.";
      default:
        return `Authentication failed: ${errorCode}`;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg
              className="h-6 w-6 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900">
            Authentication Error
          </h3>
          <p className="mt-2 text-sm text-gray-500">
            {getErrorMessage(error)}
          </p>
          <div className="mt-6">
            <button
              onClick={() => window.location.href = "/"}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Return to Home
            </button>
          </div>
          <div className="mt-4">
            <button
              onClick={() => window.location.reload()}
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
