"use client";

import { useState } from "react";
import { useTurnkeyNonce } from "@/hooks/useTurnkeyNonce";

export default function TestNoncePage() {
  const { getNonce } = useTurnkeyNonce();
  const [nonce, setNonce] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const generateNonce = async () => {
    try {
      setLoading(true);
      const generatedNonce = await getNonce();
      setNonce(generatedNonce);
      console.log("Generated Turnkey nonce:", generatedNonce);
    } catch (error) {
      console.error("Error generating nonce:", error);
    } finally {
      setLoading(false);
    }
  };

  const testGoogleLogin = async () => {
    try {
      setLoading(true);

      // Generate and store nonce
      const generatedNonce = await getNonce();
      document.cookie = `turnkey_login_nonce=${generatedNonce}; Path=/; SameSite=Lax; Secure`;
      console.log("Set nonce cookie:", generatedNonce);

      // Use custom Google OAuth flow that supports our custom nonce
      window.location.href = `/api/auth/google-custom?callbackUrl=${encodeURIComponent(
        window.location.href
      )}`;
    } catch (error) {
      console.error("Error during Google login:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mx-auto max-w-md p-8">
      <h1 className="mb-6 text-2xl font-bold">Test Nonce Integration</h1>

      <div className="space-y-4">
        <button
          onClick={generateNonce}
          disabled={loading}
          className="w-full rounded bg-blue-500 p-2 text-white disabled:opacity-50"
        >
          {loading ? "Generating..." : "Generate Turnkey Nonce"}
        </button>

        {nonce && (
          <div className="rounded bg-gray-100 p-3">
            <p className="break-all font-mono text-sm">
              <strong>Nonce:</strong> {nonce}
            </p>
          </div>
        )}

        <button
          onClick={testGoogleLogin}
          disabled={loading}
          className="w-full rounded bg-green-500 p-2 text-white disabled:opacity-50"
        >
          {loading ? "Signing in..." : "Test Google Login with Nonce"}
        </button>
      </div>

      <div className="mt-6 text-sm text-gray-600">
        <p>
          This page helps test the nonce integration between Turnkey and
          NextAuth.
        </p>
        <p className="mt-2">
          Check the browser console for nonce values and validation logs.
        </p>
      </div>
    </div>
  );
}
