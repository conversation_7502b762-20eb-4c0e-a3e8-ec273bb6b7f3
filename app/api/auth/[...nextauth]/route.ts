import NextAuth from "next-auth";
import { handleCallbackProvider, handleLoginSuccess } from "../_helpers";
import { cookies } from "next/headers";
import { jwtDecode } from "jwt-decode";

// Custom Google provider that allows nonce injection
const CustomGoogleProvider = {
  id: "google",
  name: "Google",
  type: "oauth" as const,
  authorization: async (params: any) => {
    const cookieStore = await cookies();
    const customNonce = cookieStore.get("turnkey_login_nonce")?.value;

    const authUrl = new URL("https://accounts.google.com/o/oauth2/v2/auth");
    authUrl.searchParams.set(
      "client_id",
      process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!
    );
    authUrl.searchParams.set("redirect_uri", params.redirect_uri);
    authUrl.searchParams.set("response_type", "code");
    authUrl.searchParams.set("scope", "openid email profile");
    authUrl.searchParams.set("state", params.state);
    authUrl.searchParams.set("code_challenge", params.code_challenge);
    authUrl.searchParams.set("code_challenge_method", "S256");

    // Use our custom nonce if available
    if (customNonce) {
      authUrl.searchParams.set("nonce", customNonce);
    }

    return authUrl.toString();
  },
  token: "https://oauth2.googleapis.com/token",
  userinfo: "https://openidconnect.googleapis.com/v1/userinfo",
  clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
  checks: ["pkce", "state"],
  profile(profile: any) {
    return {
      id: profile.sub,
      name: profile.name,
      email: profile.email,
      image: profile.picture,
    };
  },
};

const authOptions = {
  session: { strategy: "jwt" as const },
  providers: [CustomGoogleProvider as any],
  callbacks: {
    async signIn({ account, profile, user }: any) {
      const accessOrIdToken = account?.id_token;
      if (!accessOrIdToken) {
        return `/auth/error?error=NoAccessToken`;
      }

      // Validate the OIDC nonce echoed in the id_token against the pre-set cookie
      try {
        const decoded: any = jwtDecode(accessOrIdToken);
        const idTokenNonce: string | undefined = decoded?.nonce;
        const cookieStore = await cookies();
        const expectedNonce = cookieStore.get("turnkey_login_nonce")?.value;

        const [, payload] = accessOrIdToken.split(".");
        const test = JSON.parse(atob(payload));
        console.log(
          { decoded, idTokenNonce, expectedNonce, accessOrIdToken, test },
          "nonce"
        );
        if (!idTokenNonce || !expectedNonce || idTokenNonce !== expectedNonce) {
          return `/auth/error?error=InvalidNonce`;
        }
      } catch (e) {
        return `/auth/error?error=InvalidIdToken`;
      }

      try {
        const provider = account?.provider || "google";
        const email = profile?.email || user?.email || null;

        const result: any = await handleCallbackProvider(
          accessOrIdToken,
          provider,
          email
        );
        await handleLoginSuccess(result?.jwtToken);

        // Clear nonce cookie after successful validation
        try {
          const responseCookies = await cookies();
          responseCookies.set("turnkey_login_nonce", "", {
            httpOnly: false,
            secure: true,
            sameSite: "lax",
            path: "/",
            expires: new Date(0),
          } as any);
        } catch {}

        return true;
      } catch (error: any) {
        console.log(error, "error");
        return `/auth/error?error=${encodeURIComponent(
          error?.message || "BackendAuthFailed"
        )}`;
      }
    },
    async jwt({ token, account }: any) {
      if (account?.id_token) token.googleIdToken = account.id_token;
      return token;
    },
    async session({ session, token }: any) {
      (session.user as any) = session.user || {};
      (session.user as any).googleIdToken = token.googleIdToken;
      return session;
    },
    async redirect({ url, baseUrl }: any) {
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
  },
};

const handler = NextAuth(authOptions as any);
export { handler as GET, handler as POST };
