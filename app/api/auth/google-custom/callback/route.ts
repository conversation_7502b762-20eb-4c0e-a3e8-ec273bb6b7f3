import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { jwtDecode } from "jwt-decode";
import { handleCallbackProvider, handleLoginSuccess } from "../../_helpers";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get("code");
    const state = searchParams.get("state");
    const error = searchParams.get("error");

    if (error) {
      return NextResponse.redirect(
        new URL(`/auth/error?error=${error}`, request.url)
      );
    }

    if (!code || !state) {
      return NextResponse.redirect(
        new URL("/auth/error?error=MissingParameters", request.url)
      );
    }

    // Validate state and get stored values
    const cookieStore = await cookies();
    const storedState = cookieStore.get("oauth_state")?.value;
    const codeVerifier = cookieStore.get("oauth_code_verifier")?.value;
    const callbackUrl = cookieStore.get("oauth_callback_url")?.value || "/";
    const expectedNonce = cookieStore.get("turnkey_login_nonce")?.value;

    if (!storedState || storedState !== state) {
      return NextResponse.redirect(
        new URL("/auth/error?error=InvalidState", request.url)
      );
    }

    if (!codeVerifier) {
      return NextResponse.redirect(
        new URL("/auth/error?error=MissingCodeVerifier", request.url)
      );
    }

    if (!expectedNonce) {
      return NextResponse.redirect(
        new URL("/auth/error?error=MissingNonce", request.url)
      );
    }

    // Exchange code for tokens
    const tokenResponse = await fetch("https://oauth2.googleapis.com/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!,
        client_secret: process.env.GOOGLE_CLIENT_SECRET!,
        code,
        grant_type: "authorization_code",
        redirect_uri: `${
          new URL(request.url).origin
        }/api/auth/google-custom/callback`,
        code_verifier: codeVerifier,
      }),
    });

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.text();
      console.error("Token exchange failed:", errorData);
      return NextResponse.redirect(
        new URL("/auth/error?error=TokenExchangeFailed", request.url)
      );
    }

    const tokens = await tokenResponse.json();
    const idToken = tokens.id_token;

    if (!idToken) {
      return NextResponse.redirect(
        new URL("/auth/error?error=NoIdToken", request.url)
      );
    }

    // Validate nonce in ID token
    try {
      const decoded: any = jwtDecode(idToken);
      const idTokenNonce = decoded?.nonce;

      if (!idTokenNonce || idTokenNonce !== expectedNonce) {
        return NextResponse.redirect(
          new URL("/auth/error?error=InvalidNonce", request.url)
        );
      }
    } catch (e) {
      return NextResponse.redirect(
        new URL("/auth/error?error=InvalidIdToken", request.url)
      );
    }

    // Get user info
    const userInfoResponse = await fetch(
      "https://openidconnect.googleapis.com/v1/userinfo",
      {
        headers: {
          Authorization: `Bearer ${tokens.access_token}`,
        },
      }
    );

    if (!userInfoResponse.ok) {
      return NextResponse.redirect(
        new URL("/auth/error?error=UserInfoFailed", request.url)
      );
    }

    const userInfo = await userInfoResponse.json();

    // Exchange with your backend
    try {
      const result = await handleCallbackProvider(
        idToken,
        "google",
        userInfo.email
      );
      await handleLoginSuccess(result.jwtToken);

      // Clean up cookies
      const responseCookies = await cookies();
      responseCookies.set("oauth_state", "", {
        expires: new Date(0),
        path: "/",
      });
      responseCookies.set("oauth_code_verifier", "", {
        expires: new Date(0),
        path: "/",
      });
      responseCookies.set("oauth_callback_url", "", {
        expires: new Date(0),
        path: "/",
      });
      responseCookies.set("turnkey_login_nonce", "", {
        expires: new Date(0),
        path: "/",
      });

      // Redirect to success page - the JWT is already stored in the cookie
      // The GoogleAuthHandler will pick up the authentication from the cookie
      return NextResponse.redirect(new URL(callbackUrl, request.url));
    } catch (error: any) {
      console.error("Backend auth failed:", error);
      return NextResponse.redirect(
        new URL(
          `/auth/error?error=${encodeURIComponent(
            error?.message || "BackendAuthFailed"
          )}`,
          request.url
        )
      );
    }
  } catch (error) {
    console.error("Error in Google custom callback:", error);
    return NextResponse.redirect(
      new URL("/auth/error?error=CallbackError", request.url)
    );
  }
}
