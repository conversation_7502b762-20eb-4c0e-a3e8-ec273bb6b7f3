import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const callbackUrl = searchParams.get("callbackUrl") || "/";
    
    // Get the custom nonce from cookie
    const cookieStore = await cookies();
    const customNonce = cookieStore.get("turnkey_login_nonce")?.value;
    
    if (!customNonce) {
      return NextResponse.redirect(new URL("/auth/error?error=NoNonce", request.url));
    }

    // Build the Google OAuth authorization URL with custom nonce
    const authUrl = new URL("https://accounts.google.com/o/oauth2/v2/auth");
    
    // Generate state and code challenge for PKCE
    const state = crypto.randomUUID();
    const codeVerifier = generateCodeVerifier();
    const codeChallenge = await generateCodeChallenge(codeVerifier);
    
    // Store state and code verifier in cookies for validation
    const responseCookies = await cookies();
    responseCookies.set("oauth_state", state, {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      maxAge: 600, // 10 minutes
    });
    responseCookies.set("oauth_code_verifier", codeVerifier, {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      maxAge: 600, // 10 minutes
    });
    responseCookies.set("oauth_callback_url", callbackUrl, {
      httpOnly: true,
      secure: true,
      sameSite: "lax",
      path: "/",
      maxAge: 600, // 10 minutes
    });

    // Set authorization parameters
    authUrl.searchParams.set("client_id", process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!);
    authUrl.searchParams.set("redirect_uri", `${new URL(request.url).origin}/api/auth/google-custom/callback`);
    authUrl.searchParams.set("response_type", "code");
    authUrl.searchParams.set("scope", "openid email profile");
    authUrl.searchParams.set("state", state);
    authUrl.searchParams.set("code_challenge", codeChallenge);
    authUrl.searchParams.set("code_challenge_method", "S256");
    authUrl.searchParams.set("nonce", customNonce); // Use our custom nonce
    
    return NextResponse.redirect(authUrl.toString());
  } catch (error) {
    console.error("Error in Google custom auth:", error);
    return NextResponse.redirect(new URL("/auth/error?error=AuthError", request.url));
  }
}

// Helper functions for PKCE
function generateCodeVerifier(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return btoa(String.fromCharCode(...array))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

async function generateCodeChallenge(verifier: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(verifier);
  const digest = await crypto.subtle.digest('SHA-256', data);
  return btoa(String.fromCharCode(...new Uint8Array(digest)))
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}
