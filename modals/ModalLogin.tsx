import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import {
  setIsShowModalLogin,
  setIsShowModalConnectTelegram,
} from "@/store/metadata.store";
import { Logo, Telegram, CloseIcon } from "@/assets/icons";
import ReactModal from "react-modal";
import { AppButton } from "../components/AppButton";
import {
  ConnectButton,
  useCurrentAccount,
  useDisconnectWallet,
} from "@mysten/dapp-kit";
import { useTurnkeyNonce } from "@/hooks/useTurnkeyNonce";

export const ModalLogin = () => {
  const isOpen = useSelector(
    (state: RootState) => state.metadata.isShowModalLogin
  );
  const dispatch = useDispatch<AppDispatch>();
  const currentAccount = useCurrentAccount();
  const { mutate: disconnect } = useDisconnectWallet();
  const { getNonce } = useTurnkeyNonce();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const onClose = () => {
    if (!loading) dispatch(setIsShowModalLogin({ isShow: false }));
  };

  const onConnectTelegram = () => {
    dispatch(setIsShowModalConnectTelegram({ isShow: true }));
    dispatch(setIsShowModalLogin({ isShow: false }));
  };

  const startGoogleLogin = async () => {
    try {
      setError("");
      setLoading(true);

      // If a wallet is connected, disconnect to avoid overlapping auth states
      if (currentAccount?.address) {
        try {
          disconnect();
        } catch {}
      }

      // Generate Turnkey nonce and store it in cookie
      const nonce = await getNonce();
      document.cookie = `turnkey_login_nonce=${nonce}; Path=/; SameSite=Lax; Secure`;

      // Use custom Google OAuth flow that supports our custom nonce
      const currentUrl = window.location.href;
      window.location.href = `/api/auth/google-custom?callbackUrl=${encodeURIComponent(
        currentUrl
      )}`;
    } catch (e: any) {
      console.error(e);
      setError(e?.message || "Google sign-in failed");
      setLoading(false);
    }
  };

  const customStyles = {
    content: {
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      marginRight: "-50%",
      transform: "translate(-50%, -50%)",
      borderRadius: "16px",
      padding: 0,
      background: "#141518",
      border: "1px solid rgba(255, 255, 255, 0.05)",
      overflow: "inherit",
      boxShadow: "4px 4px 8px 0px rgba(8, 9, 12, 0.50)",
    },
    overlay: {
      background: "rgba(8, 9, 12, 0.70)",
      backdropFilter: "blur(7.5px)",
      zIndex: 999,
    },
  };

  return (
    <ReactModal
      isOpen={isOpen}
      style={customStyles}
      ariaHideApp={false}
      bodyOpenClassName="overflow-hidden"
      onRequestClose={onClose}
    >
      <div className="tablet:p-[56px] relative w-[calc(100vw-32px)] max-w-[888px] overflow-hidden rounded-[16px] p-4">
        <div
          className="text-white-500 hover:text-white-1000 absolute right-7 top-7 cursor-pointer"
          onClick={onClose}
        >
          <CloseIcon />
        </div>
        <div className="flex flex-col items-center">
          <div className="mb-6">
            <Logo />
          </div>

          <div className="text-brand-500 tablet:text-[40px] text-center text-[32px] font-semibold leading-[1.2]">
            Trade, snipe, copy trade on SUI at the speed of light
          </div>
          <div className="body-md-regular-14 mb-6 mt-4">
            Connect to start trading in less than 30 seconds
          </div>

          <div className="flex w-full max-w-[388px] flex-col gap-3">
            <AppButton
              onClick={onConnectTelegram}
              className="w-full items-center gap-2"
              variant="buy"
              size="large"
            >
              <Telegram />
              Connect Telegram
            </AppButton>

            <AppButton
              onClick={startGoogleLogin}
              className="w-full items-center gap-2"
              variant="primary"
              size="large"
              disabled={loading}
            >
              {/* Simple G icon */}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 48 48"
                width="18"
                height="18"
              >
                <path
                  fill="#FFC107"
                  d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12 s5.373-12,12-12c3.059,0,5.842,1.154,7.947,3.053l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24 s8.955,20,20,20s20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
                />
                <path
                  fill="#FF3D00"
                  d="M6.306,14.691l6.571,4.819C14.655,16.095,19.001,13,24,13c3.059,0,5.842,1.154,7.947,3.053l5.657-5.657 C34.046,6.053,29.268,4,24,4C16.318,4,9.824,8.254,6.306,14.691z"
                />
                <path
                  fill="#4CAF50"
                  d="M24,44c5.166,0,9.86-1.977,13.409-5.197l-6.196-5.238C29.211,35.091,26.715,36,24,36 c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.66,39.556,16.302,44,24,44z"
                />
                <path
                  fill="#1976D2"
                  d="M43.611,20.083H42V20H24v8h11.303 c-0.793,2.237-2.231,4.166-4.094,5.565l0.003-0.002l6.196,5.238C35.164,40.205,40,36,40,24C40,22.659,39.862,21.35,43.611,20.083z"
                />
              </svg>
              {loading
                ? "Continue with Google…"
                : "Sign in with Google (Turnkey)"}
            </AppButton>
            {error && (
              <>
                <div
                  className="mt-1 break-words rounded-md border border-red-300 bg-red-50 px-3 py-2 text-sm leading-[1.2] text-red-700 dark:border-red-500/40 dark:bg-red-950/40 dark:text-red-300"
                  role="alert"
                  aria-live="polite"
                >
                  {error}
                </div>
                <div className="mt-2 text-center text-xs text-neutral-400">
                  If the popup was blocked, please allow popups for this site
                  and try again.
                </div>
                <div className="mt-2 flex justify-center">
                  <button
                    type="button"
                    onClick={() => {
                      setError("");
                      startGoogleLogin();
                    }}
                    className="text-brand-500 hover:text-brand-400 underline"
                    disabled={loading}
                  >
                    Try again
                  </button>
                </div>
              </>
            )}
          </div>

          {!currentAccount?.address && (
            <ConnectButton
              className="!mt-10 !bg-transparent !p-0 !shadow-none"
              connectText={
                <div className="body-md-medium-14 !text-white-1000 flex cursor-pointer items-center gap-2">
                  Or Trading With Wallet{" "}
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M12.4697 5.46967C12.7626 5.17678 13.2374 5.17678 13.5303 5.46967L19.5303 11.4697C19.8232 11.7626 19.8232 12.2374 19.5303 12.5303L13.5303 18.5303C13.2374 18.8232 12.7626 18.8232 12.4697 18.5303C12.1768 18.2374 12.1768 17.7626 12.4697 17.4697L17.1893 12.75H5C4.58579 12.75 4.25 12.4142 4.25 12C4.25 11.5858 4.58579 11.25 5 11.25H17.1893L12.4697 6.53033C12.1768 6.23744 12.1768 5.76256 12.4697 5.46967Z"
                      fill="white"
                    />
                  </svg>
                </div>
              }
            />
          )}
        </div>
      </div>
    </ReactModal>
  );
};
